@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for mobile-first design with DaisyUI */

/* Output area styling */
.output-area {
  @apply min-h-[12rem] p-4 bg-base-200 border border-base-300 rounded-lg text-sm leading-relaxed overflow-y-auto mb-4;
}

/* Markdown styling within output area */
.output-area h1,
.output-area h2,
.output-area h3,
.output-area h4,
.output-area h5,
.output-area h6 {
  @apply mt-6 mb-2 font-semibold;
}

.output-area h1 { @apply text-2xl; }
.output-area h2 { @apply text-xl; }
.output-area h3 { @apply text-lg; }
.output-area h4 { @apply text-base; }
.output-area h5 { @apply text-sm; }
.output-area h6 { @apply text-xs; }

.output-area p {
  @apply mb-4;
}

.output-area ul,
.output-area ol {
  @apply mb-4 pl-6;
}

.output-area li {
  @apply mb-1;
}

.output-area blockquote {
  @apply my-4 p-4 border-l-4 border-primary bg-base-300 italic;
}

.output-area code {
  @apply bg-base-300 px-1 py-0.5 rounded font-mono text-xs;
}

.output-area pre {
  @apply bg-base-300 p-4 rounded overflow-x-auto my-4;
}

.output-area pre code {
  @apply bg-transparent p-0;
}

.output-area table {
  @apply w-full border-collapse my-4;
}

.output-area th,
.output-area td {
  @apply p-2 border border-base-300 text-left;
}

.output-area th {
  @apply bg-base-300 font-semibold;
}

.output-area strong {
  @apply font-semibold;
}

.output-area em {
  @apply italic;
}

.output-area a {
  @apply text-primary underline;
}

.output-area hr {
  @apply my-6 border-t border-base-300;
}

/* Mobile-first layout adjustments */
.mobile-container {
  @apply min-h-screen pb-20; /* Add padding bottom for dock */
}

/* Bottom dock styling */
.bottom-dock {
  @apply fixed bottom-0 left-0 right-0 bg-base-100 border-t border-base-300 z-50;
}

/* Status message styling */
#status {
  @apply p-3 rounded-lg font-medium text-center mt-4 transition-all duration-200;
}

#status.success {
  @apply bg-success text-success-content;
}

#status.error {
  @apply bg-error text-error-content;
}

#status.info {
  @apply bg-info text-info-content;
}

/* Loading animation */
.loading-custom {
  @apply inline-block w-4 h-4 border-2 border-base-300 rounded-full border-t-primary animate-spin;
}
