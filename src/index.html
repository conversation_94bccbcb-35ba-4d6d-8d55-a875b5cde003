<!doctype html>
<html lang="en" dir="ltr" data-theme="light">
  <head>
    <meta charset="UTF-8" />
    <title>SUtils</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <link rel="icon" type="image/x-icon" href="./assets/icon/favicon.ico" />
    <link rel="manifest" href="./manifest.json" />
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="./css/style.css" />
    <meta name="theme-color" content="#000000" />
  </head>
  <body class="bg-base-100">
    <div class="mobile-container">
      <!-- Top Navigation -->
      <div class="navbar bg-base-100 border-b border-base-300">
        <div class="navbar-start">
          <h1 class="text-xl font-bold">S Utils</h1>
        </div>
        <div class="navbar-end">
          <button id="configBtn" class="btn btn-ghost btn-sm" aria-label="Configure API Key">
            <i data-lucide="settings" class="w-5 h-5"></i>
          </button>
          <button id="themeToggle" class="btn btn-ghost btn-sm" aria-label="Toggle theme">
            <i data-lucide="moon" class="w-5 h-5"></i>
          </button>
        </div>
      </div>

      <!-- Configuration Modal -->
      <dialog id="configModal" class="modal">
        <div class="modal-box">
          <div class="flex justify-between items-center mb-4">
            <h3 class="font-bold text-lg">Configuration</h3>
            <button id="closeModal" class="btn btn-sm btn-circle btn-ghost">
              <i data-lucide="x" class="w-4 h-4"></i>
            </button>
          </div>

          <div class="space-y-4">
            <div class="form-control">
              <label class="label" for="apiKey">
                <span class="label-text">OpenRouter API Key</span>
              </label>
              <input type="password" id="apiKey" placeholder="sk-or-v1-..." class="input input-bordered w-full" />
              <label class="label">
                <span class="label-text-alt">Get your API key from <a href="https://openrouter.ai/keys" target="_blank" class="link link-primary">OpenRouter</a></span>
              </label>
            </div>

            <div class="form-control">
              <label class="label" for="modelSelect">
                <span class="label-text">AI Model</span>
              </label>
              <select id="modelSelect" class="select select-bordered w-full" required>
                <option selected disabled value="">Select AI model...</option>
                <option value="google/gemma-3-4b-it:free">Gemma 3 4b (Google)</option>
                <option value="mistralai/devstral-small-2505:free">Devstral Small 2505 (Mistral)</option>
                <option value="qwen/qwen3-4b:free">Qwen3 4B (Qwen)</option>
                <option value="openai/gpt-4.1-nano">GPT-4.1 Nano (OpenAI)</option>
                <option value="openai/gpt-4o-mini">GPT-4o Mini (OpenAI)</option>
                <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet (Anthropic)</option>
                <option value="anthropic/claude-3-haiku">Claude 3 Haiku (Anthropic)</option>
                <option value="google/gemini-pro-1.5">Gemini Pro 1.5 (Google)</option>
                <option value="meta-llama/llama-3.1-8b-instruct">Llama 3.1 8B (Meta)</option>
                <option value="meta-llama/llama-3.1-70b-instruct">Llama 3.1 70B (Meta)</option>
                <option value="mistralai/mistral-7b-instruct">Mistral 7B (Mistral AI)</option>
                <option value="cohere/command-r-plus">Command R+ (Cohere)</option>
              </select>
              <label class="label">
                <span class="label-text-alt">Choose the AI model for text processing. Different models have varying capabilities and costs.</span>
              </label>
            </div>

            <button id="saveApiKey" class="btn btn-primary w-full">Save Configuration</button>
          </div>
        </div>
        <form method="dialog" class="modal-backdrop">
          <button>close</button>
        </form>
      </dialog>

      <!-- Main Content -->
      <div class="p-4 space-y-6">
        <!-- Custom Prompts Section -->
        <div class="collapse collapse-arrow bg-base-200">
          <input type="checkbox" />
          <div class="collapse-title text-lg font-medium">
            Custom Prompts
          </div>
          <div class="collapse-content space-y-4">
            <div class="grid grid-cols-3 gap-2">
              <div class="col-span-2">
                <label class="label" for="savedPrompts">
                  <span class="label-text">Saved Prompts</span>
                </label>
                <select id="savedPrompts" class="select select-bordered w-full select-sm">
                  <option value="">Select a saved prompt...</option>
                </select>
              </div>
              <div class="flex items-end">
                <button id="loadPrompt" class="btn btn-outline btn-sm w-full">Load</button>
              </div>
            </div>

            <div class="form-control">
              <label class="label" for="promptSlug">
                <span class="label-text">Prompt Name</span>
              </label>
              <input type="text" id="promptSlug" placeholder="e.g., code-reviewer, translator..." class="input input-bordered input-sm" />
            </div>

            <div class="form-control">
              <label class="label" for="systemPrompt">
                <span class="label-text">System Prompt</span>
              </label>
              <textarea id="systemPrompt" placeholder="You are a helpful assistant that..." rows="4" class="textarea textarea-bordered"></textarea>
            </div>

            <div class="grid grid-cols-2 gap-2">
              <button id="savePrompt" class="btn btn-outline btn-sm">Save Prompt</button>
              <button id="deletePrompt" class="btn btn-outline btn-secondary btn-sm">Delete</button>
            </div>
          </div>
        </div>

        <!-- Input Section -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h3 class="card-title text-lg">Input</h3>
            <textarea id="inputText" placeholder="Enter your text here..." rows="8" class="textarea textarea-bordered w-full"></textarea>
            <div class="card-actions justify-end">
              <div class="grid grid-cols-2 gap-2 w-full">
                <button id="processBtn" class="btn btn-primary">
                  <i id="processIcon" data-lucide="play" class="w-4 h-4"></i>
                  <span id="processText">Process Text</span>
                </button>
                <button id="clearBtn" class="btn btn-outline">
                  <i data-lucide="x" class="w-4 h-4"></i>
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Output Section -->
        <div class="card bg-base-200">
          <div class="card-body">
            <h3 class="card-title text-lg">Output</h3>
            <div id="outputText" class="output-area">Results will appear here...</div>
            <div class="card-actions justify-end">
              <button id="copyBtn" class="btn btn-outline w-full">
                <i data-lucide="copy" class="w-4 h-4"></i>
                Copy
              </button>
            </div>
          </div>
        </div>

        <!-- Status Section -->
        <div id="status" role="status" aria-live="polite"></div>
      </div>

    </div>

    <!-- Bottom Dock Navigation -->
    <div class="bottom-dock">
      <div class="btm-nav">
        <button class="utility-btn active" data-task="chat">
          <i data-lucide="message-circle" class="w-5 h-5"></i>
          <span class="btm-nav-label">Chat</span>
        </button>
        <button class="utility-btn" data-task="grammar">
          <i data-lucide="spell-check" class="w-5 h-5"></i>
          <span class="btm-nav-label">Grammar</span>
        </button>
        <button class="utility-btn" data-task="enhance">
          <i data-lucide="sparkles" class="w-5 h-5"></i>
          <span class="btm-nav-label">Enhance</span>
        </button>
        <button class="utility-btn" data-task="summarize">
          <i data-lucide="file-text" class="w-5 h-5"></i>
          <span class="btm-nav-label">Summarize</span>
        </button>
      </div>
    </div>

    <script src="./js/sutils.js" type="module"></script>
  </body>
</html>
