// SUtils
// OpenRouter API Integration

class SUtils {
    constructor() {
        this.apiKey = localStorage.getItem('openrouter_api_key') || '';
        this.selectedModel = localStorage.getItem('selected_model') || 'openai/gpt-4o';
        this.currentTask = 'grammar';
        this.isProcessing = false;
        this.theme = localStorage.getItem('theme') || 'system';
        this.savedPrompts = JSON.parse(localStorage.getItem('saved_prompts') || '{}');

        this.systemPrompts = {
            chat: "You are a helpful, knowledgeable, and friendly AI assistant. Engage in natural conversation, answer questions, provide explanations, and assist with various tasks. Be conversational, informative, and helpful.",
            grammar: "You are a grammar and writing assistant. Fix grammar, spelling, punctuation, and improve clarity while maintaining the original meaning and tone. Return only the corrected text without explanations.",
            enhance: "You are a writing enhancement assistant. Improve the text by making it more engaging, clear, and well-structured while maintaining the original meaning. Return only the enhanced text.",
            summarize: "You are a summarization assistant. Create a concise, clear summary of the provided text that captures the key points and main ideas. Return only the summary.",
            custom: ""
        };

        this.init();
    }

    init() {
        this.initTheme();
        this.bindEvents();
        this.loadApiKey();
        this.loadSavedPrompts();
        this.updateUI();
        this.initLucideIcons();
        this.checkApiKeyOnStartup();
    }

    initTheme() {
        // Set theme based on stored preference or system preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            this.theme = savedTheme;
        } else {
            this.theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        this.applyTheme();
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.setAttribute('data-lucide', this.theme === 'dark' ? 'sun' : 'moon');
            if (window.lucide) {
                window.lucide.createIcons();
            }
        }
    }

    toggleTheme() {
        this.theme = this.theme === 'dark' ? 'light' : 'dark';
        localStorage.setItem('theme', this.theme);
        this.applyTheme();
    }

    initLucideIcons() {
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    bindEvents() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // Configuration modal
        document.getElementById('configBtn').addEventListener('click', () => this.openConfigModal());
        document.getElementById('closeModal').addEventListener('click', () => this.closeConfigModal());

        // Custom prompts
        document.getElementById('savePrompt').addEventListener('click', () => this.saveCustomPrompt());
        document.getElementById('loadPrompt').addEventListener('click', () => this.loadCustomPrompt());
        document.getElementById('deletePrompt').addEventListener('click', () => this.deleteCustomPrompt());

        // API Key management
        document.getElementById('saveApiKey').addEventListener('click', () => this.saveApiKey());
        document.getElementById('apiKey').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.saveApiKey();
        });

        // Utility buttons
        document.querySelectorAll('.utility-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const task = e.currentTarget.dataset.task;
                this.selectTask(task);
            });
        });

        // Main actions
        document.getElementById('processBtn').addEventListener('click', () => this.processText());
        document.getElementById('clearBtn').addEventListener('click', () => this.clearText());
        document.getElementById('copyBtn').addEventListener('click', () => this.copyOutput());

        // Enter key in input text
        document.getElementById('inputText').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.processText();
            }
        });

        // Close modal on backdrop click
        document.getElementById('configModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeConfigModal();
            }
        });
    }

    loadApiKey() {
        if (this.apiKey) {
            document.getElementById('apiKey').value = this.apiKey;
        }
        // Load selected model
        document.getElementById('modelSelect').value = this.selectedModel;
    }

    checkApiKeyOnStartup() {
        // Show modal if no API key is set
        if (!this.apiKey) {
            this.openConfigModal();
        }
    }

    openConfigModal() {
        const modal = document.getElementById('configModal');
        modal.showModal();
        // Focus on the API key input
        setTimeout(() => {
            document.getElementById('apiKey').focus();
        }, 100);
    }

    closeConfigModal() {
        const modal = document.getElementById('configModal');
        modal.close();
    }

    loadSavedPrompts() {
        const select = document.getElementById('savedPrompts');
        // Clear existing options except the first one
        select.innerHTML = '<option value="">Select a saved prompt...</option>';

        // Add saved prompts to dropdown
        Object.keys(this.savedPrompts).forEach(slug => {
            const option = document.createElement('option');
            option.value = slug;
            option.textContent = slug;
            select.appendChild(option);
        });
    }

    saveCustomPrompt() {
        const slugInput = document.getElementById('promptSlug');
        const promptInput = document.getElementById('systemPrompt');
        const slug = slugInput.value.trim();
        const prompt = promptInput.value.trim();

        if (!slug) {
            this.showStatus('Please enter a prompt name', 'error');
            return;
        }

        if (!prompt) {
            this.showStatus('Please enter a system prompt', 'error');
            return;
        }

        // Validate slug format (alphanumeric, hyphens, underscores)
        if (!/^[a-zA-Z0-9-_]+$/.test(slug)) {
            this.showStatus('Prompt name can only contain letters, numbers, hyphens, and underscores', 'error');
            return;
        }

        // Save prompt
        this.savedPrompts[slug] = prompt;
        localStorage.setItem('saved_prompts', JSON.stringify(this.savedPrompts));

        // Refresh dropdown
        this.loadSavedPrompts();

        // Clear inputs
        slugInput.value = '';
        promptInput.value = '';

        this.showStatus(`Prompt "${slug}" saved successfully!`, 'success');
    }

    loadCustomPrompt() {
        const select = document.getElementById('savedPrompts');
        const selectedSlug = select.value;

        if (!selectedSlug) {
            this.showStatus('Please select a prompt to load', 'error');
            return;
        }

        const prompt = this.savedPrompts[selectedSlug];
        if (prompt) {
            document.getElementById('systemPrompt').value = prompt;
            document.getElementById('promptSlug').value = selectedSlug;
            this.showStatus(`Prompt "${selectedSlug}" loaded`, 'success');
        } else {
            this.showStatus('Prompt not found', 'error');
        }
    }

    deleteCustomPrompt() {
        const select = document.getElementById('savedPrompts');
        const selectedSlug = select.value;

        if (!selectedSlug) {
            this.showStatus('Please select a prompt to delete', 'error');
            return;
        }

        if (confirm(`Are you sure you want to delete the prompt "${selectedSlug}"?`)) {
            delete this.savedPrompts[selectedSlug];
            localStorage.setItem('saved_prompts', JSON.stringify(this.savedPrompts));

            // Refresh dropdown
            this.loadSavedPrompts();

            // Clear inputs
            document.getElementById('promptSlug').value = '';
            document.getElementById('systemPrompt').value = '';

            this.showStatus(`Prompt "${selectedSlug}" deleted`, 'success');
        }
    }

    saveApiKey() {
        const apiKeyInput = document.getElementById('apiKey');
        const modelSelect = document.getElementById('modelSelect');
        const apiKey = apiKeyInput.value.trim();
        const selectedModel = modelSelect.value;

        if (!apiKey) {
            this.showStatus('Please enter an API key', 'error');
            return;
        }

        if (!selectedModel) {
            this.showStatus('Please select an AI model', 'error');
            return;
        }

        this.apiKey = apiKey;
        this.selectedModel = selectedModel;
        localStorage.setItem('openrouter_api_key', apiKey);
        localStorage.setItem('selected_model', selectedModel);
        this.showStatus('Configuration saved successfully!', 'success');
        this.updateUI();
        this.closeConfigModal();
    }

    selectTask(task) {
        this.currentTask = task;

        // Update button states for bottom navigation
        document.querySelectorAll('.utility-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-task="${task}"]`).classList.add('active');

        // Update system prompt if custom
        if (task === 'custom') {
            document.getElementById('systemPrompt').style.display = 'block';
        } else {
            document.getElementById('systemPrompt').style.display = 'none';
        }

        this.showStatus(`Selected: ${this.getTaskName(task)}`, 'info');
    }

    getTaskName(task) {
        const names = {
            chat: 'Chat',
            grammar: 'Grammar Fix',
            enhance: 'Text Enhancement',
            summarize: 'Summarization',
            custom: 'Custom Agent'
        };
        return names[task] || task;
    }

    async processText() {
        const inputText = document.getElementById('inputText').value.trim();

        if (!inputText) {
            this.showStatus('Please enter some text to process', 'error');
            return;
        }

        if (!this.apiKey) {
            this.showStatus('Please set your OpenRouter API key first', 'error');
            return;
        }

        if (this.isProcessing) {
            return;
        }

        this.isProcessing = true;
        this.updateUI();
        this.showStatus('Processing...', 'info');

        try {
            const result = await this.callOpenRouter(inputText);
            this.renderOutput(result);
            this.showStatus('Processing completed!', 'success');
        } catch (error) {
            console.error('Error:', error);
            this.showStatus(`Error: ${error.message}`, 'error');
            document.getElementById('outputText').textContent = 'An error occurred while processing your request.';
        } finally {
            this.isProcessing = false;
            this.updateUI();
        }
    }

    async callOpenRouter(inputText) {
        let systemPrompt = this.systemPrompts[this.currentTask];

        if (this.currentTask === 'custom') {
            systemPrompt = document.getElementById('systemPrompt').value.trim();
            if (!systemPrompt) {
                throw new Error('Please enter a custom system prompt');
            }
        }

        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'HTTP-Referer': window.location.origin,
                'X-Title': 'SUtils',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: this.selectedModel,
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: inputText
                    }
                ],
                temperature: 0.7,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('Invalid response format from API');
        }

        return data.choices[0].message.content;
    }

    renderOutput(text) {
        const outputElement = document.getElementById('outputText');

        // Check if marked library is available
        if (typeof marked !== 'undefined') {
            // Configure marked for security
            marked.setOptions({
                breaks: true,
                gfm: true,
                sanitize: false, // We trust the AI output
                smartLists: true,
                smartypants: true
            });

            // Render markdown to HTML
            outputElement.innerHTML = marked.parse(text);
        } else {
            // Fallback to plain text if marked is not available
            outputElement.textContent = text;
        }
    }

    clearText() {
        document.getElementById('inputText').value = '';
        document.getElementById('outputText').innerHTML = 'Results will appear here...';
        this.showStatus('Text cleared', 'info');
    }

    async copyOutput() {
        const outputElement = document.getElementById('outputText');
        const outputText = outputElement.textContent || outputElement.innerText;

        if (outputText === 'Results will appear here...' || !outputText.trim()) {
            this.showStatus('No output to copy', 'error');
            return;
        }

        try {
            await navigator.clipboard.writeText(outputText);
            this.showStatus('Output copied to clipboard!', 'success');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = outputText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showStatus('Output copied to clipboard!', 'success');
        }
    }

    updateUI() {
        const processBtn = document.getElementById('processBtn');
        const processIcon = document.getElementById('processIcon');
        const processText = document.getElementById('processText');
        const hasApiKey = !!this.apiKey;

        processBtn.disabled = this.isProcessing || !hasApiKey;

        if (this.isProcessing) {
            processBtn.classList.add('loading');
            processIcon.innerHTML = '<span class="loading loading-spinner loading-sm"></span>';
            processText.textContent = 'Processing...';
        } else {
            processBtn.classList.remove('loading');
            if (!hasApiKey) {
                processIcon.setAttribute('data-lucide', 'alert-triangle');
                processText.textContent = 'Set API Key First';
            } else {
                processIcon.setAttribute('data-lucide', 'play');
                processText.textContent = 'Process Text';
            }
        }

        // Refresh Lucide icons
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = type;

        // Auto-hide after 5 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                statusElement.textContent = '';
                statusElement.className = '';
            }, 5000);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sutils = new SUtils();

    // Set default task
    window.sutils.selectTask('chat');

    // Initialize Lucide icons after a short delay to ensure they're loaded
    setTimeout(() => {
        if (window.lucide) {
            window.lucide.createIcons();
        }
    }, 100);
});
